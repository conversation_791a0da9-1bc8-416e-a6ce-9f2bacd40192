"""
Gacha系統用戶收藏存儲庫 (Asyncpg 版本)
負責管理用戶卡片收藏的基本操作和查詢
"""

import json
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Literal, Optional, Tuple, TypedDict, Union

import asyncpg
import redis.asyncio as redis

# 使用統一的排序配置
from gacha.config.sorting_config import SortingConfig
from gacha.constants import RarityLevel
from gacha.exceptions import (
    CacheError,
    CardNotFoundError,
    DatabaseOperationError,
    DataMappingError,
    EntityNotFoundError,
    InsufficientCardQuantityError,
    InvalidOperationError,
    RepositoryError,
)
from gacha.models.filters import CollectionFilters
from gacha.models.models import Card, SeriesCollection, UserCard
from gacha.repositories._base_repo import (
    execute_many,
    execute_query,
    fetch_all,
    fetch_one,
    fetch_value,
)
from gacha.repositories.sql_queries import GET_USER_CARD_POSITION_SQL
from utils.logger import logger

# --- 類型定義 ---

SellOperationDetailType = Literal[
    "sell_specific_quantity_of_card", "sell_all_for_card", "sell_leaving_one_for_card"
]


class CardSellOperation(TypedDict):
    card_id: int
    operation_type: SellOperationDetailType
    current_quantity_owned: int
    quantity_to_sell: Optional[int]


class DeletedCardInfo(TypedDict):
    card_id: int
    quantity: int
    rarity: int
    pool_type: str
    name: str
    is_favorite: bool


class AffectedCardSummary(TypedDict):
    card_id: int
    name: str
    rarity: int
    pool_type: str
    quantity_sold: int
    is_favorite: bool


# --- 模組級常數 ---

TABLE_NAME = "gacha_user_collections"
OWNER_COUNT_CACHE_PREFIX = "gacha:owner_count:"
OWNER_COUNT_CACHE_TTL = timedelta(hours=1)

_BASE_USER_CARD_FIELDS = """
    uc.id, uc.user_id, uc.card_id, uc.quantity, uc.first_acquired,
    uc.last_acquired, uc.is_favorite, uc.star_level, uc.custom_sort_index,
    mc.name, mc.series, mc.rarity, mc.image_url, mc.description,
    mc.current_market_sell_price, mc.creation_date, mc.pool_type,
    mc.original_id,
    cd.description AS custom_description, cd.user_id AS description_user_id
"""

_sorting_clause_cache = {}

# --- 內部輔助函式 ---


def _get_base_user_card_select_query() -> str:
    """返回獲取用戶卡片基礎信息的 SELECT 和 FROM/JOIN 子句"""
    return f"""
        SELECT {_BASE_USER_CARD_FIELDS}
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        LEFT JOIN gacha_card_descriptions cd ON uc.card_id = cd.card_id AND cd.user_id = uc.user_id
    """


def _create_user_card_from_result(result: asyncpg.Record) -> UserCard:
    """從查詢結果創建UserCard對象 (內部輔助) - 失敗時拋出異常"""
    try:
        card = Card.from_db_record(dict(result))
        user_card = UserCard(
            id=result["id"],
            user_id=result["user_id"],
            card_id=result["card_id"],
            card=card,
            quantity=result["quantity"],
            first_acquired=result["first_acquired"],
            last_acquired=result["last_acquired"],
            is_favorite=result.get("is_favorite", False),
            star_level=result.get("star_level", 0),
            custom_sort_index=result.get("custom_sort_index"),
        )
        user_card.custom_description = result.get("custom_description")
        user_card.description_user_id = result.get("description_user_id")
        return user_card
    except KeyError as e:
        logger.error(
            "[GACHA] _create_user_card: 創建UserCard時數據中缺少鍵 %s: %s",
            e,
            result,
            exc_info=True,
        )
        raise DataMappingError(f"創建UserCard對象時數據欄位缺失: {e}") from e
    except ValueError as e:
        logger.error(
            "[GACHA] _create_user_card: 創建Card實例時出錯: %s", e, exc_info=True
        )
        raise DataMappingError(f"創建UserCard依賴的Card對象時出錯: {e}") from e
    except Exception as e:
        logger.error(
            "[GACHA] _create_user_card: 創建UserCard對象時發生未知錯誤: %s",
            e,
            exc_info=True,
        )
        raise DataMappingError(f"創建UserCard對象時發生未知錯誤: {e}") from e


def _process_paginated_results(results: List[asyncpg.Record]) -> List[UserCard]:
    """處理分頁查詢結果 (內部輔助) - 失敗時內部會拋出 DataMappingError"""
    user_cards = []
    if not results:
        return user_cards
    for result_row in results:
        user_cards.append(_create_user_card_from_result(result_row))
    return user_cards


def _add_basic_filter_conditions(
    clauses: List[str], params: List[Any], param_index: int, filters: CollectionFilters
) -> int:
    """添加基本篩選條件（pool_type, rarity, series）"""
    # 優先使用 pool_type_in（複選），如果沒有則使用 pool_type（單選）
    if filters.pool_type_in:
        clauses.append(f"mc.pool_type = ANY(${param_index}::text[])")
        params.append(filters.pool_type_in)
        param_index += 1
    elif filters.pool_type:
        clauses.append(f"mc.pool_type = ${param_index}")
        params.append(filters.pool_type)
        param_index += 1

    if filters.rarity_in:
        clauses.append(f"mc.rarity = ANY(${param_index}::integer[])")
        params.append(filters.rarity_in)
        param_index += 1

    if filters.rarity_not_in:
        clauses.append(f"mc.rarity <> ALL(${param_index}::integer[])")
        params.extend(filters.rarity_not_in)
        param_index += 1

    if filters.series:
        clauses.append(f"mc.series = ${param_index}")
        params.append(filters.series)
        param_index += 1

    if filters.series_not_in:
        clauses.append(f"mc.series <> ALL(${param_index}::text[])")
        params.extend(filters.series_not_in)
        param_index += 1

    return param_index


def _add_quantity_filter_conditions(
    clauses: List[str], params: List[Any], param_index: int, filters: CollectionFilters
) -> int:
    """添加數量相關篩選條件"""
    if filters.quantity_greater_than is not None and filters.quantity_greater_than > 0:
        clauses.append(f"uc.quantity > ${param_index}")
        params.append(filters.quantity_greater_than)
        param_index += 1

    if filters.only_duplicates:
        clauses.append("uc.quantity > 1")

    return param_index


def _add_card_filter_conditions(
    clauses: List[str], params: List[Any], param_index: int, filters: CollectionFilters
) -> int:
    """添加卡片相關篩選條件"""
    if filters.card_id is not None:
        clauses.append(f"uc.card_id = ${param_index}")
        params.append(filters.card_id)
        param_index += 1

    if filters.card_name is not None:
        clauses.append(f"mc.name ILIKE ${param_index}")
        params.append(f"%{filters.card_name}%")
        param_index += 1

    return param_index


def _add_extra_filter_conditions(
    clauses: List[str], filters: CollectionFilters
) -> None:
    """添加額外篩選條件"""
    # This function is now reserved for other extra filters, favorite logic is handled separately.
    pass


def _add_favorite_filter_conditions(
    clauses: List[str], filters: CollectionFilters
) -> None:
    """添加最愛篩選條件"""
    if filters.favorite_status == "favorite_only":
        clauses.append("uc.is_favorite = TRUE")
    elif filters.favorite_status == "non_favorite_only":
        clauses.append("(uc.is_favorite = FALSE OR uc.is_favorite IS NULL)")


def _build_filter_conditions(
    user_id: int, filters: Optional[CollectionFilters] = None
) -> Tuple[str, List[Any]]:
    """(Asyncpg) 構建篩選條件和參數，使用 $N 佔位符"""
    clauses = []
    params = []
    param_index = 1

    # 添加基本用戶ID條件
    clauses.append(f"uc.user_id = ${param_index}")
    params.append(user_id)
    param_index += 1

    if filters is None:
        return (" AND ".join(clauses), params)

    # 添加各種篩選條件
    param_index = _add_basic_filter_conditions(clauses, params, param_index, filters)
    param_index = _add_quantity_filter_conditions(clauses, params, param_index, filters)
    param_index = _add_card_filter_conditions(clauses, params, param_index, filters)
    _add_extra_filter_conditions(clauses, filters)
    _add_favorite_filter_conditions(clauses, filters)

    return (" AND ".join(clauses), params)


def _build_sorting_clause(
    sort_by: str, sort_order: str, favorite_priority: bool = True
) -> str:
    """構建通用的排序子句 (內部輔助) - 統一實現選項 B"""
    global _sorting_clause_cache
    cache_key = f"favorite_priority-{sort_by}-{sort_order}-{favorite_priority}"
    if cache_key in _sorting_clause_cache:
        return _sorting_clause_cache[cache_key]

    # 標準化參數
    default_sort_by = sort_by.lower() if sort_by else SortingConfig.DEFAULT_SORT_BY
    default_sort_order = (
        sort_order.lower()
        if sort_order in ["asc", "desc"]
        else SortingConfig.DEFAULT_SORT_ORDER
    )
    default_sort_dir = "ASC" if default_sort_order == "asc" else "DESC"
    text_nulls_order = "NULLS LAST"
    numeric_nulls_order = "NULLS LAST"
    rarity_sort_dir = "DESC" if default_sort_order == "desc" else "ASC"

    # 獲取排序字段
    sort_field_map = SortingConfig.get_db_field_map()
    default_sort_field = sort_field_map.get(default_sort_by)

    order_clauses = []

    # 自定義排序模式：最愛卡片優先
    if favorite_priority:
        order_clauses.append(
            "CASE WHEN uc.custom_sort_index IS NULL THEN 1 ELSE 0 END ASC"
        )
        order_clauses.append("uc.custom_sort_index ASC NULLS LAST")

    # 添加主要排序字段（統一排序和自定義排序都需要）
    _add_primary_sort_field(
        order_clauses,
        default_sort_field,
        default_sort_by,
        default_sort_dir,
        rarity_sort_dir,
        text_nulls_order,
        numeric_nulls_order,
    )

    # 最終排序：card_id 升序
    order_clauses.append("uc.card_id ASC")

    final_clause_string = ", ".join(order_clauses)
    _sorting_clause_cache[cache_key] = final_clause_string
    logger.debug(
        "[GACHA][SORT][CLAUSE] Built sorting clause for '%s': %s",
        cache_key,
        final_clause_string,
    )
    return final_clause_string


def _add_primary_sort_field(
    order_clauses: list,
    default_sort_field: Optional[str],
    default_sort_by: str,
    default_sort_dir: str,
    rarity_sort_dir: str,
    text_nulls_order: str,
    numeric_nulls_order: str,
) -> None:
    """添加主要排序字段到排序子句中"""
    if default_sort_field:
        # 文本字段使用文本排序
        if default_sort_field in SortingConfig.TEXT_FIELDS:
            order_clauses.append(
                f"{default_sort_field} {default_sort_dir} {text_nulls_order}"
            )
        else:
            # 數值字段，稀有度特殊處理
            sort_direction = (
                rarity_sort_dir if default_sort_by == "rarity" else default_sort_dir
            )

            # 特殊處理需要 COALESCE 的字段（統一處理 NULL 值）
            if default_sort_by in ["owner_count", "price", "wishlist_count"]:
                order_clauses.append(
                    f"COALESCE({default_sort_field}, 0) {sort_direction} {numeric_nulls_order}"
                )
            else:
                order_clauses.append(
                    f"{default_sort_field} {sort_direction} {numeric_nulls_order}"
                )
    else:
        # 無效排序字段，回退到稀有度排序
        logger.warning(
            "Invalid default sort_by value: %s. Falling back to rarity sort.",
            default_sort_by,
        )
        sort_field_map = SortingConfig.get_db_field_map()
        order_clauses.append(f"{sort_field_map['rarity']} DESC {numeric_nulls_order}")


# --- 快取相關函式 ---


async def _invalidate_owner_count_cache(
    redis_client: redis.Redis, card_id: int
) -> None:
    """(Async) 失效單個卡片的擁有者數量快取"""
    try:
        cache_key = f"{OWNER_COUNT_CACHE_PREFIX}{card_id}"
        await redis_client.delete(cache_key)
        logger.debug(
            "[GACHA][CACHE] Invalidated owner count cache for card_id: %s", card_id
        )
    except Exception as cache_err:
        logger.error(
            "[GACHA][CACHE] Failed to invalidate owner count cache for card %s: %s",
            card_id,
            cache_err,
            exc_info=True,
        )


async def _invalidate_owner_count_cache_batch(
    redis_client: redis.Redis, card_ids: List[int]
) -> None:
    """(Async) 批量失效多個卡片的擁有者數量快取"""
    if not card_ids:
        return
    start_time = time.monotonic()
    num_card_ids = len(card_ids)
    logger.debug("[CACHE_INVALIDATE_BATCH] Starting for %s card_ids.", num_card_ids)
    try:
        keys_to_delete = [
            f"{OWNER_COUNT_CACHE_PREFIX}{card_id}" for card_id in card_ids
        ]
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        duration = (time.monotonic() - start_time) * 1000
        logger.debug(
            "[CACHE_INVALIDATE_BATCH] Completed for %s card_ids. Duration: %sms",
            num_card_ids,
            duration,
        )
    except Exception as cache_err:
        duration = (time.monotonic() - start_time) * 1000
        logger.error(
            "[CACHE_INVALIDATE_BATCH] Failed for %s card_ids after %sms. Error: %s",
            num_card_ids,
            duration,
            cache_err,
            exc_info=True,
        )


# --- 主要儲存庫函式 ---


async def add_card_and_check_new(
    redis_client: redis.Redis,
    user_id: int,
    card_id: int,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """(Async) 向用戶收藏中添加卡片，同時檢查該卡片是否為新卡，並在需要時清除快取."""
    now = datetime.now()
    query = f"""
        INSERT INTO {TABLE_NAME} (user_id, card_id, quantity, first_acquired, last_acquired, star_level)
        VALUES ($1, $2, 1, $3, $4, 0)
        ON CONFLICT (user_id, card_id) DO UPDATE
        SET quantity = {TABLE_NAME}.quantity + 1,
            last_acquired = EXCLUDED.last_acquired
        RETURNING (xmax = 0) AS is_new, star_level, quantity
    """
    result = await fetch_one(query, (user_id, card_id, now, now), connection=connection)
    if not result:
        raise DatabaseOperationError(
            f"添加/更新卡片操作失敗，資料庫未返回結果: user_id={user_id}, card_id={card_id}"
        )
    is_new = result.get("is_new")
    quantity = result.get("quantity")
    star_level = result.get("star_level")
    if is_new is None:
        logger.error(
            "[GACHA_INTEGRITY_ERROR] add_card_and_check_new: 'is_new' field was None in RETURNING clause for user_id=%s, card_id=%s. Result: %s",
            user_id,
            card_id,
            result,
        )
        raise DatabaseOperationError(
            f"添加/更新卡片時 'is_new' 欄位返回結果異常 for user_id={user_id}, card_id={card_id}"
        )
    if quantity is None:
        logger.error(
            "[GACHA_INTEGRITY_ERROR] add_card_and_check_new: 'quantity' field was None in RETURNING clause for user_id=%s, card_id=%s. Result: %s",
            user_id,
            card_id,
            result,
        )
        raise DatabaseOperationError(
            f"添加/更新卡片時 'quantity' 欄位返回結果異常 for user_id={user_id}, card_id={card_id}"
        )
    if star_level is None:
        logger.error(
            "[GACHA_INTEGRITY_ERROR] add_card_and_check_new: 'star_level' field was None in RETURNING clause for user_id=%s, card_id=%s. Result: %s",
            user_id,
            card_id,
            result,
        )
        raise DatabaseOperationError(
            f"添加/更新卡片時 'star_level' 欄位返回結果異常 for user_id={user_id}, card_id={card_id}"
        )
    if is_new:
        try:
            await _invalidate_owner_count_cache(redis_client, card_id)
        except CacheError as ce:
            logger.warning(
                "Cache invalidation failed for new card %s for user %s but continuing: %s",
                card_id,
                user_id,
                ce,
            )
        except Exception as e_cache:
            logger.warning(
                "Generic cache invalidation error for new card %s for user %s but continuing: %s",
                card_id,
                user_id,
                e_cache,
            )
    return {"is_new": is_new, "star_level": star_level, "quantity": quantity}


async def bulk_add_cards_and_check_new(
    redis_client: redis.Redis,
    user_id: int,
    card_ids: List[int],
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[int, Dict[str, Any]]:
    """(Async) 批量向用戶收藏中添加卡片，同時檢查是否為新卡，並在需要時清除快取."""
    if not card_ids:
        return {}
    card_id_counts = {}
    for card_id_val in card_ids:
        card_id_counts[card_id_val] = card_id_counts.get(card_id_val, 0) + 1
    now = datetime.now()
    results_dict: Dict[int, Dict[str, Any]] = {}
    newly_added_card_ids: List[int] = []
    input_data_for_query = list(card_id_counts.items())
    card_id_list = [item[0] for item in input_data_for_query]
    quantity_to_add_list = [item[1] for item in input_data_for_query]
    query = f"""
        WITH input_cards AS (
            SELECT
                u.card_id,
                u.quantity_to_add
            FROM unnest($2::integer[], $3::integer[]) AS u(card_id, quantity_to_add)
        ),
        upserted AS (
            INSERT INTO {TABLE_NAME} (user_id, card_id, quantity, first_acquired, last_acquired, star_level)
            SELECT $1, ic.card_id, ic.quantity_to_add, $4, $4, 0
            FROM input_cards ic
            ON CONFLICT (user_id, card_id) DO UPDATE
            SET quantity = {TABLE_NAME}.quantity + EXCLUDED.quantity,
                last_acquired = EXCLUDED.last_acquired
            RETURNING card_id, (xmax = 0) AS is_new, star_level, quantity
        )
        SELECT card_id, is_new, star_level, quantity FROM upserted;
    """
    rows = await fetch_all(
        query, (user_id, card_id_list, quantity_to_add_list, now), connection=connection
    )
    if not rows and card_ids:
        raise DatabaseOperationError(
            f"批量添加/更新卡片操作未返回任何行 for user_id={user_id}"
        )
    for row in rows:
        op_card_id = row["card_id"]
        is_new = row["is_new"]
        results_dict[op_card_id] = {
            "is_new": is_new,
            "star_level": row["star_level"],
            "quantity": row["quantity"],
        }
        if is_new:
            newly_added_card_ids.append(op_card_id)
    if newly_added_card_ids:
        try:
            await _invalidate_owner_count_cache_batch(
                redis_client, newly_added_card_ids
            )
        except CacheError as ce:
            logger.warning(
                "Batch cache invalidation failed for new cards user %s but continuing: %s",
                user_id,
                ce,
            )
        except Exception as e_cache:
            logger.warning(
                "Generic batch cache invalidation error for new cards user %s but continuing: %s",
                user_id,
                e_cache,
            )
    return results_dict


async def _remove_logic(
    redis_client: redis.Redis,
    conn: asyncpg.Connection,
    user_id: int,
    card_id: int,
    quantity_to_remove: int,
) -> Dict[str, Any]:
    check_query = f"SELECT quantity FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2 FOR UPDATE"
    check_result = await conn.fetchrow(check_query, user_id, card_id)
    if not check_result:
        raise CardNotFoundError(f"您沒有這張卡片 (ID: {card_id})", card_id=card_id)
    current_quantity = check_result["quantity"]
    if current_quantity < quantity_to_remove:
        raise InsufficientCardQuantityError(
            f"卡片數量不足: user_id={user_id}, card_id={card_id}, need={quantity_to_remove}, have={current_quantity}",
            required=quantity_to_remove,
            current=current_quantity,
        )
    remaining_quantity = current_quantity - quantity_to_remove
    was_fully_removed = False
    if remaining_quantity > 0:
        update_query = (
            f"UPDATE {TABLE_NAME} SET quantity = $1 WHERE user_id = $2 AND card_id = $3"
        )
        status = await execute_query(
            update_query, (remaining_quantity, user_id, card_id), connection=conn
        )
        if status != 1:
            raise DatabaseOperationError(
                f"更新卡片數量失敗 for user={user_id}, card={card_id}, status={status}"
            )
    else:
        delete_query = f"DELETE FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2"
        status = await execute_query(delete_query, (user_id, card_id), connection=conn)
        if status == 1:
            was_fully_removed = True
            try:
                await _invalidate_owner_count_cache(redis_client, card_id)
            except CacheError as ce:
                logger.warning(
                    "Cache invalidation failed for removed card %s for user %s but continuing: %s",
                    card_id,
                    user_id,
                    ce,
                )
            except Exception as e_cache:
                logger.warning(
                    "Generic cache invalidation error for removed card %s for user %s but continuing: %s",
                    card_id,
                    user_id,
                    e_cache,
                )
        else:
            raise DatabaseOperationError(
                f"刪除卡片記錄失敗 for user={user_id}, card={card_id}, status={status}"
            )
    return {
        "remaining_quantity": remaining_quantity,
        "was_fully_removed": was_fully_removed,
    }


async def remove_card(
    redis_client: redis.Redis,
    user_id: int,
    card_id: int,
    connection: asyncpg.Connection,
    quantity_to_remove: int = 1,
) -> Dict[str, Any]:
    """(Async) 從用戶收藏中移除指定數量的卡片。此方法必須在一個外部事務中被調用。"""
    if quantity_to_remove <= 0:
        raise InvalidOperationError(f"無效的移除數量: {quantity_to_remove}")
    if not connection:
        raise ValueError(
            "remove_card 必須在一個事務中並提供一個有效的 asyncpg Connection。"
        )
    return await _remove_logic(
        redis_client, connection, user_id, card_id, quantity_to_remove
    )


async def get_user_card(
    user_id: int, card_id: int, connection: Optional[asyncpg.Connection] = None
) -> UserCard:
    """(Async) 根據用戶ID和卡片ID獲取單個用戶卡片 - 未找到則拋出 CardNotFoundError"""
    base_query = _get_base_user_card_select_query()
    query = f"{base_query} WHERE uc.user_id = $1 AND uc.card_id = $2"
    result = await fetch_one(query, (user_id, card_id), connection=connection)
    if not result:
        raise CardNotFoundError(f"您沒有這張卡片 (ID: {card_id})", card_id=card_id)
    return _create_user_card_from_result(result)


async def get_user_collection_by_id(
    collection_id: int, connection: Optional[asyncpg.Connection] = None
) -> UserCard:
    """(Async) 根據收藏ID獲取單個用戶卡片"""
    base_query = _get_base_user_card_select_query()
    query = f"{base_query} WHERE uc.id = $1"
    result = await fetch_one(query, (collection_id,), connection=connection)
    if not result:
        raise EntityNotFoundError(f"找不到 ID 為 {collection_id} 的收藏記錄")
    return _create_user_card_from_result(result)


async def get_user_card_for_update(
    user_id: int, card_id: int, connection: asyncpg.Connection
) -> UserCard:
    """(Async) 根據用戶ID和卡片ID獲取單個用戶卡片，並鎖定該行以供更新。"""
    if connection is None:
        raise ValueError(
            "get_user_card_for_update 必須在事務中並提供一個有效的 asyncpg Connection。"
        )
    base_query = _get_base_user_card_select_query()
    query = f"{base_query} WHERE uc.user_id = $1 AND uc.card_id = $2 FOR UPDATE OF uc"
    result = await connection.fetchrow(query, user_id, card_id)
    if not result:
        raise CardNotFoundError(
            f"嘗試鎖定更新時，找不到用戶 {user_id} 的卡片 {card_id}", card_id=card_id
        )
    return _create_user_card_from_result(result)


async def get_user_cards_paginated(
    redis_client: redis.Redis,
    user_id: int,
    page: int = 1,
    page_size: int = 1,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    filters: Optional[CollectionFilters] = None,
    connection: Optional[asyncpg.Connection] = None,
    *,
    pre_fetched_unique_cards: Optional[int] = None,
    pre_fetched_total_cards: Optional[int] = None,
    favorite_priority: bool = True,
) -> Dict[str, Any]:
    """(Async) 獲取用戶卡片分頁數據. Can accept pre-fetched stats to avoid re-querying them."""
    if filters is None:
        filters = CollectionFilters()
    built_where_clause, built_params = _build_filter_conditions(user_id, filters)
    unique_cards_stat: int
    total_cards_stat: int
    if pre_fetched_unique_cards is not None and pre_fetched_total_cards is not None:
        logger.debug(
            "[GACHA_REPO_PAGINATED] Using pre-fetched stats: unique=%s, total=%s",
            pre_fetched_unique_cards,
            pre_fetched_total_cards,
        )
        unique_cards_stat = pre_fetched_unique_cards
        total_cards_stat = pre_fetched_total_cards
    elif pre_fetched_unique_cards is not None:
        logger.debug(
            "[GACHA_REPO_PAGINATED] Using pre-fetched unique_cards: %s. Total quantity will be queried.",
            pre_fetched_unique_cards,
        )
        unique_cards_stat = pre_fetched_unique_cards
        stats = await get_user_collection_stats(
            user_id, filters, where_clause=built_where_clause, params=built_params
        )
        total_cards_stat = stats.get("total_quantity", 0)
    else:
        logger.debug(
            "[GACHA_REPO_PAGINATED] No/incomplete pre-fetched stats. Querying all stats."
        )
        stats = await get_user_collection_stats(
            user_id, filters, where_clause=built_where_clause, params=built_params
        )
        total_cards_stat = stats.get("total_quantity", 0)
        unique_cards_stat = stats.get("unique_cards", 0)
    if unique_cards_stat == 0:
        return {
            "cards": [],
            "total_pages": 0,
            "current_page": 1,
            "total_cards": 0,
            "unique_cards": 0,
        }
    total_pages = max(1, (unique_cards_stat + page_size - 1) // page_size)
    current_page = max(1, min(page, total_pages))
    cards = await _fetch_user_cards_for_page(
        redis_client,
        user_id,
        current_page,
        page_size,
        sort_by,
        sort_order,
        filters,
        where_clause=built_where_clause,
        params_list=built_params,
        connection=connection,
        favorite_priority=favorite_priority,
    )
    return {
        "cards": cards,
        "total_pages": total_pages,
        "current_page": current_page,
        "total_cards": total_cards_stat,
        "unique_cards": unique_cards_stat,
    }


async def _fetch_user_cards_for_page(
    redis_client: redis.Redis,
    user_id: int,
    page: int,
    page_size: int,
    sort_by: str,
    sort_order: str,
    filters: CollectionFilters,
    *,
    where_clause: str,
    params_list: List[Any],
    connection: Optional[asyncpg.Connection] = None,
    favorite_priority: bool = True,
) -> List[UserCard]:
    """(Internal Async) Fetches a single page of user cards based on criteria - 優化版本使用分離查詢"""
    # 動態導入以避免循環依賴
    from gacha.repositories.card import master_card_repository

    offset = (page - 1) * page_size
    query_params = list(params_list)
    limit_placeholder = f"${len(query_params) + 1}"
    offset_placeholder = f"${len(query_params) + 2}"
    # 根據排序字段決定是否需要 JOIN market_stats
    market_stats_join = ""
    if sort_by in ["owner_count", "wishlist_count"]:
        market_stats_join = (
            "LEFT JOIN gacha_card_market_stats ms ON uc.card_id = ms.card_id"
        )

    user_collection_query = f"""
        SELECT uc.id, uc.user_id, uc.card_id, uc.quantity, uc.first_acquired,
               uc.last_acquired, uc.is_favorite, uc.star_level, uc.custom_sort_index,
               mc.rarity, mc.name,
               cd.description AS custom_description, cd.user_id AS description_user_id
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        LEFT JOIN gacha_card_descriptions cd ON uc.card_id = cd.card_id AND cd.user_id = uc.user_id
        {market_stats_join}
        WHERE {where_clause}
        ORDER BY {_build_sorting_clause(sort_by, sort_order, favorite_priority)}
        LIMIT {limit_placeholder} OFFSET {offset_placeholder}
    """
    query_params.extend([page_size, offset])
    collection_results = await fetch_all(
        user_collection_query, query_params, connection=connection
    )
    if not collection_results:
        return []
    card_ids = [row["card_id"] for row in collection_results]

    master_cards = await master_card_repository.get_cards_details_by_ids(
        card_ids=card_ids
    )
    card_map = {card.card_id: card for card in master_cards}
    user_cards = []
    for row in collection_results:
        card_id = row["card_id"]
        master_card = card_map.get(card_id)
        if master_card:
            user_card = UserCard(
                id=row["id"],
                user_id=row["user_id"],
                card_id=card_id,
                card=master_card,
                quantity=row["quantity"],
                first_acquired=row["first_acquired"],
                last_acquired=row["last_acquired"],
                is_favorite=row["is_favorite"],
                star_level=row["star_level"],
                custom_sort_index=row["custom_sort_index"],
                custom_description=row.get("custom_description"),
                description_user_id=row.get("description_user_id"),
                owner_count=0,
            )
            user_cards.append(user_card)
    await _attach_owner_counts_to_cards(redis_client, user_cards)
    return user_cards


async def get_filtered_card_ids(
    user_id: int,
    filters: Optional[CollectionFilters] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> List[int]:
    """(Async) 獲取經過過濾的用戶卡片ID列表."""
    if filters is None:
        filters = CollectionFilters()
    where_clause, params = _build_filter_conditions(user_id, filters)
    query = f"SELECT uc.card_id FROM {TABLE_NAME} uc JOIN gacha_master_cards mc ON uc.card_id = mc.card_id WHERE {where_clause}"
    results = await fetch_all(query, params, connection=connection)
    return [row["card_id"] for row in results]


async def get_cards_for_autocomplete(
    user_id: int,
    filters: Optional[CollectionFilters] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> List[Dict[str, Any]]:
    """(Async) 專為自動完成優化的輕量級查詢，只獲取必要欄位。"""
    if filters is None:
        filters = CollectionFilters()
    where_clause, params = _build_filter_conditions(user_id, filters)

    # 只選擇自動完成所需的最小欄位集
    query = f"""
        SELECT
            mc.card_id,
            mc.name,
            uc.quantity
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE {where_clause}
        ORDER BY mc.rarity DESC, mc.name ASC
        LIMIT 25
    """
    results = await fetch_all(query, params, connection=connection)
    # 直接返回字典列表，避免昂貴的物件映射
    return [dict(row) for row in results]


async def find_user_card_by_name(
    user_id: int, filters: CollectionFilters
) -> Dict[str, Any]:
    """(Async) 根據名稱模糊搜索用戶擁有的卡片."""
    if not filters.card_name:
        raise ValueError("缺少卡片名稱進行搜索")
    where_clause, params = _build_filter_conditions(user_id, filters)
    query = f"""
        SELECT mc.card_id, mc.name as card_name
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE {where_clause}
        ORDER BY mc.name
        LIMIT 1
    """
    result = await fetch_one(query, params)
    if not result:
        raise CardNotFoundError(f"找不到名為 '{filters.card_name}' 的卡片")
    return dict(result)


async def get_user_cards_page_only(
    redis_client: redis.Redis,
    user_id: int,
    page: int = 1,
    page_size: int = 9,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    filters: Optional[CollectionFilters] = None,
    connection: Optional[asyncpg.Connection] = None,
    favorite_priority: bool = True,
) -> Dict[str, Any]:
    """(Async) 僅獲取特定頁面的卡片列表 - 異常將從 _fetch_user_cards_for_page 傳播."""
    if filters is None:
        filters = CollectionFilters()
    where_clause, params_list = _build_filter_conditions(user_id, filters)
    user_cards = await _fetch_user_cards_for_page(
        redis_client,
        user_id,
        page,
        page_size,
        sort_by,
        sort_order,
        filters,
        where_clause=where_clause,
        params_list=params_list,
        connection=connection,
        favorite_priority=favorite_priority,
    )
    logger.debug(
        "[GACHA_DIAGNOSIS] UserCollectionRepository.get_user_cards_page_only: Returning data: {'cards': len(user_cards) if user_cards else 0, 'current_page': %s, ...}",
        page,
    )
    return {"cards": user_cards, "current_page": page, "total_pages": 1}


async def get_star_levels_batch(user_id: int, card_ids: List[int]) -> Dict[int, int]:
    """(Async) 批量獲取多張卡片的星級."""
    if not card_ids:
        return {}
    query = f"SELECT card_id, star_level FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = ANY($2::integer[])"
    results = await fetch_all(query, [user_id, card_ids])
    return {row["card_id"]: row["star_level"] for row in results}


async def get_favorite_status_batch(
    user_id: int, card_ids: List[int]
) -> Dict[int, bool]:
    """(Async) 批量獲取多張卡片的最愛狀態."""
    if not card_ids:
        return {}
    query = f"SELECT card_id, is_favorite FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = ANY($2::integer[])"
    results = await fetch_all(query, [user_id, card_ids])
    return {row["card_id"]: row.get("is_favorite", False) for row in results}


async def check_cards_owned_batch(user_id: int, card_ids: List[int]) -> Dict[int, bool]:
    """(Async) 批量檢查用戶是否擁有多張卡片."""
    if not card_ids:
        return {}
    query = f"SELECT card_id FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = ANY($2::integer[])"
    results = await fetch_all(query, [user_id, card_ids])
    owned_card_ids = {row["card_id"] for row in results}
    return {card_id: card_id in owned_card_ids for card_id in card_ids}


async def update_star_level(
    user_id: int,
    card_id: int,
    new_star_level: int,
    connection: Optional[asyncpg.Connection] = None,
) -> None:
    """(Async) 更新用戶卡片的星級."""
    if new_star_level < 0:
        raise ValueError(f"無效星級: {new_star_level}")
    query = (
        f"UPDATE {TABLE_NAME} SET star_level = $1 WHERE user_id = $2 AND card_id = $3"
    )
    status = await execute_query(
        query, (new_star_level, user_id, card_id), connection=connection
    )
    if status != 1:
        raise DatabaseOperationError(
            f"更新星級操作未影響任何行或找不到記錄: user_id={user_id}, card_id={card_id}"
        )


async def _attach_owner_counts_to_cards(
    redis_client: redis.Redis, user_cards: List[UserCard]
) -> None:
    """(Async) 批量獲取並附加 owner_count 到 UserCard 列表。"""
    if not user_cards:
        return
    card_ids = [uc.card_id for uc in user_cards if uc]
    if not card_ids:
        return
    owner_counts = await get_card_owner_counts_batch(redis_client, card_ids)
    for uc in user_cards:
        if uc:
            uc.owner_count = owner_counts.get(uc.card_id, 0)


async def get_card_owner_count(redis_client: redis.Redis, card_id: int) -> int:
    """(Async) 獲取指定卡片的獨立擁有者數量 (使用快取). Returns 0 on DB/general error."""
    start_time = time.monotonic()
    cache_key = f"{OWNER_COUNT_CACHE_PREFIX}{card_id}"
    owner_count = 0
    cache_hit = False
    try:
        cached_count_str = await redis_client.get(cache_key)
        if cached_count_str is not None:
            try:
                owner_count = int(cached_count_str)
                cache_hit = True
                logger.debug(
                    "[GACHA][PERF] get_card_owner_count for card %s (cache_hit=True) took %sms. Count: %s",
                    card_id,
                    (time.monotonic() - start_time) * 1000,
                    owner_count,
                )
                return owner_count
            except ValueError:
                logger.warning(
                    "[GACHA][CACHE] Invalid owner count value in cache for %s: %s. Will query DB.",
                    cache_key,
                    cached_count_str,
                )
    except redis.RedisError as cache_read_err:
        logger.error(
            "[GACHA][CACHE] Redis error reading owner count from cache for card %s: %s",
            card_id,
            cache_read_err,
            exc_info=True,
        )
    query = f"SELECT COUNT(DISTINCT user_id) FROM {TABLE_NAME} WHERE card_id = $1"
    db_count = await fetch_value(query, (card_id,))
    owner_count = db_count if db_count is not None else 0
    try:
        await redis_client.set(cache_key, owner_count, ex=OWNER_COUNT_CACHE_TTL)
    except redis.RedisError as cache_write_err:
        logger.error(
            "[GACHA][CACHE] Redis error writing owner count to cache for card %s: %s",
            card_id,
            cache_write_err,
            exc_info=True,
        )
    duration_ms = (time.monotonic() - start_time) * 1000
    logger.debug(
        "[GACHA][PERF] get_card_owner_count for card %s (cache_hit=%s) took %sms. Count: %s",
        card_id,
        cache_hit,
        duration_ms,
        owner_count,
    )
    return owner_count


async def _process_cached_owner_counts(
    redis_client: redis.Redis,
    unique_card_ids: List[int],
    cache_keys_map: Dict[int, str],
) -> Tuple[Dict[int, int], List[int]]:
    """處理快取中的擁有者數量數據"""
    results: Dict[int, int] = {}
    ids_to_fetch_from_db: List[int] = list(unique_card_ids)

    try:
        cached_values_list = await redis_client.mget(list(cache_keys_map.values()))
        temp_ids_still_missing = []

        for i, card_id_from_unique_list in enumerate(unique_card_ids):
            if i < len(cached_values_list) and cached_values_list[i] is not None:
                try:
                    results[card_id_from_unique_list] = int(cached_values_list[i])
                except ValueError:
                    logger.warning(
                        "[GACHA][CACHE] Invalid batch owner count value in cache for card %s: %s. Will query DB.",
                        card_id_from_unique_list,
                        cached_values_list[i],
                    )
                    temp_ids_still_missing.append(card_id_from_unique_list)
            else:
                temp_ids_still_missing.append(card_id_from_unique_list)

        ids_to_fetch_from_db = temp_ids_still_missing
    except redis.RedisError as cache_read_err:
        logger.error(
            "[GACHA][CACHE] Redis error during batch read of owner counts. Will attempt to fetch all unique IDs from DB. Error: %s",
            cache_read_err,
            exc_info=True,
        )
        ids_to_fetch_from_db = list(unique_card_ids)

    return results, ids_to_fetch_from_db


async def _fetch_owner_counts_from_db(ids_to_fetch: List[int]) -> Dict[int, int]:
    """從數據庫獲取擁有者數量"""
    query = f"SELECT card_id, COUNT(DISTINCT user_id)::int as count FROM {TABLE_NAME} WHERE card_id = ANY($1::integer[]) GROUP BY card_id"
    db_results_map: Dict[int, int] = {}

    rows = await fetch_all(query, (ids_to_fetch,))
    for row in rows:
        if row:
            db_results_map[row["card_id"]] = row["count"]

    return db_results_map


async def _cache_owner_counts(
    redis_client: redis.Redis,
    db_results_map: Dict[int, int],
    cache_keys_map: Dict[int, str],
) -> None:
    """將擁有者數量寫入快取"""
    if not db_results_map:
        return

    try:
        pipe = redis_client.pipeline(transaction=False)
        for card_id_db, count_db in db_results_map.items():
            if card_id_db in cache_keys_map:
                pipe.set(
                    cache_keys_map[card_id_db],
                    count_db,
                    ex=OWNER_COUNT_CACHE_TTL,
                )
        await pipe.execute()
    except redis.RedisError as cache_write_err:
        logger.error(
            "[GACHA][CACHE] Redis error during batch write of owner counts to cache: %s",
            cache_write_err,
            exc_info=True,
        )


async def get_card_owner_counts_batch(
    redis_client: redis.Redis, card_ids: List[int]
) -> Dict[int, int]:
    """(Async) 批量獲取多張卡片的獨立擁有者數量 (使用快取). Returns {} on DB/general error."""
    if not card_ids:
        return {}

    unique_card_ids = sorted(set(card_ids))
    if not unique_card_ids:
        return {}

    cache_keys_map = {
        cid: f"{OWNER_COUNT_CACHE_PREFIX}{cid}" for cid in unique_card_ids
    }

    # 處理快取數據
    results, ids_to_fetch_from_db = await _process_cached_owner_counts(
        redis_client, unique_card_ids, cache_keys_map
    )

    # 從數據庫獲取缺失的數據
    if ids_to_fetch_from_db:
        db_results_map = await _fetch_owner_counts_from_db(ids_to_fetch_from_db)

        # 更新結果
        for id_to_check in ids_to_fetch_from_db:
            results[id_to_check] = db_results_map.get(id_to_check, 0)

        # 寫入快取
        await _cache_owner_counts(redis_client, db_results_map, cache_keys_map)

    return results


def _validate_sell_operation_inputs(
    user_id: int, operations: List[CardSellOperation], connection: asyncpg.Connection
) -> None:
    """驗證售賣操作的輸入參數"""
    if not connection:
        logger.error(
            "[PROCESS_SELL_BATCH] User %s: Connection is required for batch processing.",
            user_id,
        )
        raise ValueError(
            "process_sell_operations_batch 必須在一個事務中並提供一個有效的 asyncpg Connection。"
        )


def _create_empty_result() -> Dict[str, Any]:
    """創建空的結果字典"""
    return {
        "message": "沒有提供任何操作。",
        "updated_cards": [],
        "deleted_cards": [],
        "failed_operations": [],
        "card_ids_for_cache_invalidation": [],
    }


def _calculate_sell_quantity(op: CardSellOperation) -> Tuple[int, Optional[str]]:
    """計算售賣數量，返回 (數量, 錯誤信息)"""
    op_type = op["operation_type"]
    current_owned = op["current_quantity_owned"]

    if op_type == "sell_specific_quantity_of_card":
        quantity_to_sell = op.get("quantity_to_sell")
        if quantity_to_sell is None or quantity_to_sell <= 0:
            return 0, "指定售賣數量無效。"
        if quantity_to_sell > current_owned:
            return (
                0,
                f"請求售賣數量 ({quantity_to_sell}) 超過擁有數量 ({current_owned})。",
            )
        return quantity_to_sell, None
    elif op_type == "sell_all_for_card":
        return current_owned, None
    elif op_type == "sell_leaving_one_for_card":
        return max(0, current_owned - 1), None
    else:
        return 0, f"未知的操作類型: {op_type}"


async def _execute_batch_updates(
    connection: asyncpg.Connection,
    updates_to_execute_values: List[Tuple[int, int, int]],
    updated_cards_summary: List[Dict[str, Any]],
    failed_operations_summary: List[Dict[str, Any]],
) -> None:
    """執行批量更新操作"""
    batch_size_updates = 800

    for i in range(0, len(updates_to_execute_values), batch_size_updates):
        batch_updates = updates_to_execute_values[i : i + batch_size_updates]
        if not batch_updates:
            continue

        update_query_batch = f"""
            UPDATE {TABLE_NAME} AS uc
            SET quantity = val.new_quantity
            FROM (VALUES {", ".join((f"(${j * 3 + 1}::integer, ${j * 3 + 2}::bigint, ${j * 3 + 3}::integer)" for j in range(len(batch_updates))))})
                AS val(new_quantity, user_id_val, card_id_val)
            WHERE uc.user_id = val.user_id_val AND uc.card_id = val.card_id_val
            RETURNING uc.card_id, uc.quantity;
        """
        flat_update_params_batch = [
            item for sublist in batch_updates for item in sublist
        ]
        updated_rows_batch = await connection.fetch(
            update_query_batch, *flat_update_params_batch
        )
        current_batch_processed_ids = {row["card_id"] for row in updated_rows_batch}
        for row in updated_rows_batch:
            updated_cards_summary.append(
                {
                    "card_id": row["card_id"],
                    "remaining_quantity": row["quantity"],
                }
            )
        failed_batch_update_ids = {
            cid for _, _, cid in batch_updates
        } - current_batch_processed_ids
        for failed_cid in failed_batch_update_ids:
            failed_operations_summary.append(
                {
                    "card_id": failed_cid,
                    "reason": "更新卡片數量時(批次)，記錄未找到或更新未生效。",
                }
            )


async def _execute_batch_deletes(
    connection: asyncpg.Connection,
    deletes_to_execute_values: List[Tuple[int, int]],
    deleted_cards_summary: List[Dict[str, Any]],
    failed_operations_summary: List[Dict[str, Any]],
    card_ids_to_invalidate_cache: List[int],
) -> None:
    """執行批量刪除操作"""
    batch_size_deletes = 1000

    for i in range(0, len(deletes_to_execute_values), batch_size_deletes):
        batch_deletes = deletes_to_execute_values[i : i + batch_size_deletes]
        if not batch_deletes:
            continue

        values_clause_delete_batch = ", ".join(
            (
                f"(${j * 2 + 1}::bigint, ${j * 2 + 2}::integer)"
                for j in range(len(batch_deletes))
            )
        )
        delete_query_batch = f"DELETE FROM {TABLE_NAME} WHERE (user_id, card_id) IN (VALUES {values_clause_delete_batch}) RETURNING card_id, is_favorite;"
        flat_delete_params_batch = [
            item for sublist in batch_deletes for item in sublist
        ]
        deleted_rows_batch = await connection.fetch(
            delete_query_batch, *flat_delete_params_batch
        )
        current_batch_processed_ids = {row["card_id"] for row in deleted_rows_batch}
        for row in deleted_rows_batch:
            deleted_card_id = row["card_id"]
            was_favorite = row["is_favorite"]
            deleted_cards_summary.append(
                {"card_id": deleted_card_id, "was_favorite": was_favorite}
            )
            card_ids_to_invalidate_cache.append(deleted_card_id)
        failed_batch_delete_ids = {
            cid for _, cid in batch_deletes
        } - current_batch_processed_ids
        for failed_cid in failed_batch_delete_ids:
            failed_operations_summary.append(
                {
                    "card_id": failed_cid,
                    "reason": "刪除卡片時(批次)，記錄未找到或刪除未生效。",
                }
            )


def _process_operations_to_db_actions(
    user_id: int,
    operations: List[CardSellOperation],
    failed_operations_summary: List[Dict[str, Any]],
) -> Tuple[List[Tuple[int, int, int]], List[Tuple[int, int]]]:
    """處理操作並生成數據庫操作列表"""
    updates_to_execute_values: List[Tuple[int, int, int]] = []
    deletes_to_execute_values: List[Tuple[int, int]] = []

    for op in operations:
        card_id = op["card_id"]
        op_type = op["operation_type"]
        current_owned = op["current_quantity_owned"]

        # 計算售賣數量
        quantity_to_sell_this_op, error_msg = _calculate_sell_quantity(op)
        if error_msg:
            failed_operations_summary.append({"card_id": card_id, "reason": error_msg})
            continue

        # 檢查是否需要執行操作
        if quantity_to_sell_this_op <= 0 and not (
            op_type == "sell_all_for_card" and current_owned == 0
        ):
            logger.debug(
                "[PROCESS_SELL_BATCH] User %s, Card %s: No quantity to sell for operation type %s, owned %s.",
                user_id,
                card_id,
                op_type,
                current_owned,
            )
            continue

        # 特殊情況：刪除數量為0的卡片
        if current_owned == 0 and op_type == "sell_all_for_card":
            logger.debug(
                "[PROCESS_SELL_BATCH] User %s, Card %s: Special case - deleting card with quantity 0.",
                user_id,
                card_id,
            )
            deletes_to_execute_values.append((user_id, card_id))
            continue

        # 決定是更新還是刪除
        remaining_quantity = current_owned - quantity_to_sell_this_op
        if remaining_quantity > 0:
            updates_to_execute_values.append((remaining_quantity, user_id, card_id))
        else:
            deletes_to_execute_values.append((user_id, card_id))

    return updates_to_execute_values, deletes_to_execute_values


async def process_sell_operations_batch(
    user_id: int, operations: List[CardSellOperation], connection: asyncpg.Connection
) -> Dict[str, Any]:
    """(Async) 通用的批量處理用戶卡片售賣操作。"""
    _validate_sell_operation_inputs(user_id, operations, connection)

    if not operations:
        return _create_empty_result()

    updated_cards_summary: List[Dict[str, Any]] = []
    deleted_cards_summary: List[Dict[str, Any]] = []
    failed_operations_summary: List[Dict[str, Any]] = []
    card_ids_to_invalidate_cache: List[int] = []

    # 處理操作並生成數據庫操作列表
    updates_to_execute_values, deletes_to_execute_values = (
        _process_operations_to_db_actions(
            user_id, operations, failed_operations_summary
        )
    )
    if (
        not updates_to_execute_values
        and (not deletes_to_execute_values)
        and failed_operations_summary
    ):
        logger.warning(
            "[PROCESS_SELL_BATCH] User %s: All operations failed pre-check or resulted in no DB action.",
            user_id,
        )
        raise DatabaseOperationError("所有提供的操作均未能執行或無需執行。")
    if (
        not updates_to_execute_values
        and (not deletes_to_execute_values)
        and (not failed_operations_summary)
    ):
        return {
            "message": "沒有需要執行的數據庫操作。",
            "updated_cards": [],
            "deleted_cards": [],
            "failed_operations": [],
            "card_ids_for_cache_invalidation": [],
        }
    try:
        # 執行批量更新操作
        if updates_to_execute_values:
            await _execute_batch_updates(
                connection,
                updates_to_execute_values,
                updated_cards_summary,
                failed_operations_summary,
            )

        # 執行批量刪除操作
        if deletes_to_execute_values:
            await _execute_batch_deletes(
                connection,
                deletes_to_execute_values,
                deleted_cards_summary,
                failed_operations_summary,
                card_ids_to_invalidate_cache,
            )
        if failed_operations_summary:
            logger.warning(
                "[PROCESS_SELL_BATCH] User %s: Some operations failed across batches. Details: %s",
                user_id,
                failed_operations_summary,
            )
            return {
                "message": f"部分售賣操作處理完成，但有 {len(failed_operations_summary)} 個操作失敗或未執行。",
                "updated_cards": updated_cards_summary,
                "deleted_cards": deleted_cards_summary,
                "failed_operations": failed_operations_summary,
                "card_ids_for_cache_invalidation": card_ids_to_invalidate_cache,
            }
        return {
            "message": "批量售賣操作成功完成。",
            "updated_cards": updated_cards_summary,
            "deleted_cards": deleted_cards_summary,
            "failed_operations": [],
            "card_ids_for_cache_invalidation": card_ids_to_invalidate_cache,
        }
    except asyncpg.PostgresError as e:
        logger.error(
            "[PROCESS_SELL_BATCH] User %s: Database error during batch sell: %s",
            user_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"批量售賣數據庫操作失敗: {e}") from e
    except Exception as e:
        logger.error(
            "[PROCESS_SELL_BATCH] User %s: Unexpected error during batch sell: %s",
            user_id,
            e,
            exc_info=True,
        )
        raise DatabaseOperationError(f"批量售賣時發生未知錯誤: {e}") from e


async def get_sellable_card_snapshots_with_stored_price(
    user_id: int,
    filters: CollectionFilters,
    connection: Optional[asyncpg.Connection] = None,
) -> List[Dict[str, Any]]:
    """(Async) 獲取符合篩選條件的用戶卡片快照，包含預存的市場價格。"""
    logger.debug(
        "[SNAPSHOT_STORED_PRICE] User %s: Getting sellable snapshots with stored price. Filters: %s",
        user_id,
        filters,
    )
    where_clause, params = _build_filter_conditions(user_id, filters)
    query = f"""
        SELECT
            uc.card_id, uc.quantity, uc.is_favorite, uc.star_level,
            mc.name AS card_name, mc.rarity AS master_rarity,
            mc.pool_type AS master_pool_type, mc.current_market_sell_price
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE {where_clause}
        ORDER BY uc.card_id;
    """
    rows = await fetch_all(query, params, connection=connection)
    snapshots = []
    if rows:
        for row_data in rows:
            try:
                snapshots.append(
                    {
                        "card_id": row_data["card_id"],
                        "quantity": row_data["quantity"],
                        "is_favorite": row_data["is_favorite"],
                        "star_level": row_data["star_level"],
                        "name": row_data["card_name"],
                        "rarity": RarityLevel(row_data["master_rarity"]),
                        "pool_type": row_data["master_pool_type"],
                        "current_market_sell_price": (
                            row_data["current_market_sell_price"]
                            if row_data["current_market_sell_price"] is not None
                            else None
                        ),
                    }
                )
            except Exception as e_map:
                logger.error(
                    "Data mapping error for snapshot: %s, row: %s",
                    e_map,
                    row_data,
                    exc_info=True,
                )
                raise DataMappingError(
                    f"Error mapping snapshot data for card_id {row_data.get('card_id', 'UNKNOWN')}"
                ) from e_map
    logger.debug(
        "[SNAPSHOT_STORED_PRICE] User %s: Found %s snapshots.", user_id, len(snapshots)
    )
    return snapshots


async def get_card_quantity(
    user_id: int, card_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """(Async) 查詢指定用戶特定卡片的數量。如果未找到或發生錯誤，返回 0。"""
    query = f"SELECT quantity FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2"
    quantity = await fetch_value(query, (user_id, card_id), connection=connection)
    return quantity if quantity is not None else 0


async def increment_card_quantity(
    redis_client: redis.Redis,
    user_id: int,
    card_id: int,
    quantity_to_add: int,
    connection: asyncpg.Connection,
) -> int:
    """(Async) 增加指定用戶特定卡片的數量。如果用戶之前沒有該卡片，則創建新記錄。"""
    if quantity_to_add <= 0:
        raise ValueError("quantity_to_add 必須為正整數")
    now = datetime.now()
    query = f"""
        INSERT INTO {TABLE_NAME} (user_id, card_id, quantity, first_acquired, last_acquired, star_level)
        VALUES ($1, $2, $3, $4, $4, 0)
        ON CONFLICT (user_id, card_id) DO UPDATE
        SET quantity = {TABLE_NAME}.quantity + EXCLUDED.quantity,
            last_acquired = EXCLUDED.last_acquired
        RETURNING quantity, (xmax = 0) AS is_new;
    """
    result = await connection.fetchrow(query, user_id, card_id, quantity_to_add, now)
    if not result:
        raise DatabaseOperationError(
            f"增加卡片數量操作失敗，資料庫未返回結果: user_id={user_id}, card_id={card_id}"
        )
    new_total_quantity = result["quantity"]
    is_new_entry = result["is_new"]
    if is_new_entry:
        try:
            await _invalidate_owner_count_cache(redis_client, card_id)
        except CacheError as ce:
            logger.warning(
                "Cache invalidation failed for new card %s (increment_card_quantity): %s",
                card_id,
                ce,
            )
        except Exception as e_generic_cache:
            logger.warning(
                "Generic cache invalidation error for new card %s (increment_card_quantity): %s",
                card_id,
                e_generic_cache,
            )
    return new_total_quantity


async def get_card_favorite_status(
    user_id: int, card_id: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """獲取指定用戶的特定卡片是否被標記為最愛。"""
    query = f"SELECT is_favorite FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2"
    result_val = await fetch_value(query, [user_id, card_id], connection=connection)
    if result_val is None:
        raise CardNotFoundError(
            f"找不到用戶 {user_id} 的卡片 {card_id}", card_id=card_id
        )
    return result_val


async def decrement_card_quantity(
    redis_client: redis.Redis,
    user_id: int,
    card_id: int,
    quantity_to_remove: int,
    connection: asyncpg.Connection,
) -> Tuple[int, bool, bool]:
    """減少指定用戶特定卡片的數量。

    Returns:
        Tuple[int, bool, bool]: (new_quantity, was_deleted, was_favorite)
        - new_quantity: 更新後的數量
        - was_deleted: 卡片是否被完全刪除
        - was_favorite: 被刪除的卡片是否為收藏狀態（只有在 was_deleted=True 時才有意義）
    """
    if quantity_to_remove <= 0:
        raise ValueError("quantity_to_remove 必須為正整數")
    get_current_query = f"SELECT quantity, is_favorite FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2 FOR UPDATE"
    current_record = await connection.fetchrow(get_current_query, user_id, card_id)
    if not current_record:
        raise CardNotFoundError(
            f"嘗試減少數量時，找不到用戶 {user_id} 的卡片 {card_id}", card_id=card_id
        )
    current_quantity = current_record["quantity"]
    current_is_favorite = current_record["is_favorite"]
    if current_quantity < quantity_to_remove:
        raise InsufficientCardQuantityError(
            f"嘗試減少的數量 ({quantity_to_remove}) 超過擁有數量 ({current_quantity}) for card {card_id}",
            required=quantity_to_remove,
            current=current_quantity,
        )
    new_quantity = current_quantity - quantity_to_remove
    was_deleted = False
    if new_quantity == 0:
        delete_query = f"DELETE FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2"
        status = await execute_query(
            delete_query, (user_id, card_id), connection=connection
        )
        if status != 1:
            raise DatabaseOperationError(
                f"刪除卡片記錄失敗 for user={user_id}, card={card_id}, status={status}"
            )
        try:
            await _invalidate_owner_count_cache(redis_client, card_id)
        except CacheError as ce:
            logger.warning(
                "Cache invalidation failed for deleted card %s (decrement_card_quantity): %s",
                card_id,
                ce,
            )
        except Exception as e_generic_cache:
            logger.warning(
                "Generic cache invalidation error for deleted card %s (decrement_card_quantity): %s",
                card_id,
                e_generic_cache,
            )
        was_deleted = True
        return (0, was_deleted, current_is_favorite)
    else:
        update_query = f"UPDATE {TABLE_NAME} SET quantity = $1 WHERE user_id = $2 AND card_id = $3 RETURNING quantity"
        updated_record = await connection.fetchrow(
            update_query, new_quantity, user_id, card_id
        )
        if not updated_record or updated_record["quantity"] != new_quantity:
            raise DatabaseOperationError(
                f"更新卡片數量失敗或返回數量不一致 for user={user_id}, card={card_id}"
            )
        return (
            new_quantity,
            was_deleted,
            False,
        )  # 沒有刪除時，was_favorite 無意義，返回 False


async def get_user_favorite_card_count(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """(Async) 獲取用戶標記為最愛的卡片總數。"""
    query = f"SELECT COUNT(*)::integer FROM {TABLE_NAME} WHERE user_id = $1 AND is_favorite = TRUE"
    count = await fetch_value(query, (user_id,), connection=connection)
    return count or 0


async def get_favorite_user_cards(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[UserCard]:
    """(Async) 獲取用戶所有標記為最愛的卡片，並按推薦順序排序。"""
    base_select_query = _get_base_user_card_select_query()
    query = f"""
        {base_select_query}
        WHERE uc.user_id = $1 AND uc.is_favorite = TRUE
        ORDER BY uc.custom_sort_index ASC NULLS LAST, mc.rarity DESC, mc.name ASC
    """
    results = await fetch_all(query, (user_id,), connection=connection)
    user_cards = []
    if results:
        for r in results:
            try:
                user_cards.append(_create_user_card_from_result(r))
            except DataMappingError as e:
                logger.error(
                    "Error mapping favorite card data for user %s, record: %s, error: %s",
                    user_id,
                    r,
                    e,
                )
                raise
    return user_cards


async def get_card_owners_list(
    card_id: int,
    page: int = 1,
    per_page: int = 20,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """(Async) 獲取指定卡片的擁有者列表（分頁）"""
    offset = (page - 1) * per_page

    # 獲取總數
    count_query = f"""
        SELECT COUNT(DISTINCT uc.user_id) as total_count
        FROM {TABLE_NAME} uc
        WHERE uc.card_id = $1 AND uc.quantity > 0
    """
    count_result = await fetch_one(count_query, (card_id,), connection=connection)
    total_count = count_result["total_count"] if count_result else 0

    # 獲取分頁數據
    query = f"""
        SELECT
            uc.user_id,
            uc.quantity,
            uc.star_level,
            uc.is_favorite,
            uc.first_acquired
        FROM {TABLE_NAME} uc
        WHERE uc.card_id = $1 AND uc.quantity > 0
        ORDER BY uc.first_acquired ASC
        LIMIT $2 OFFSET $3
    """
    results = await fetch_all(query, (card_id, per_page, offset), connection=connection)

    owners = []
    if results:
        for row in results:
            owners.append(
                {
                    "user_id": row["user_id"],
                    "quantity": row["quantity"],
                    "star_level": row["star_level"],
                    "is_favorite": row["is_favorite"],
                    "first_acquired": row["first_acquired"],
                }
            )

    total_pages = (total_count + per_page - 1) // per_page

    return {
        "owners": owners,
        "current_page": page,
        "total_pages": max(1, total_pages),
        "total_count": total_count,
        "per_page": per_page,
    }


# --- 統計 (Stats) Mixin 函式 ---


async def get_user_collection_stats(
    user_id: int,
    filters: Optional[CollectionFilters] = None,
    *,
    where_clause: Optional[str] = None,
    params: Optional[List[Any]] = None,
) -> Dict[str, int]:
    """(Async) 獲取用戶收藏統計信息 (優化為單一查詢)"""
    where_clause_internal: str
    params_internal: List[Any]
    if where_clause is not None and params is not None:
        where_clause_internal = where_clause
        params_internal = list(params)
    else:
        if filters is None:
            filters = CollectionFilters()
        w_clause, p_list = _build_filter_conditions(user_id, filters)
        where_clause_internal = w_clause
        params_internal = p_list
    combined_query = f"""
        SELECT
            COUNT(DISTINCT uc.card_id)::integer as unique_cards,
            COALESCE(SUM(uc.quantity), 0)::integer as total_quantity
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE {where_clause_internal}
    """
    result = await fetch_one(combined_query, params_internal)
    if result:
        unique_cards = result.get("unique_cards", 0)
        total_quantity = result.get("total_quantity", 0)
    else:
        unique_cards = 0
        total_quantity = 0
        logger.warning(
            "[GACHA] get_user_collection_stats: 查詢未返回結果 for user_id=%s, filters=%s",
            user_id,
            filters,
        )
    return {"unique_cards": unique_cards, "total_quantity": total_quantity}


async def _get_all_series(
    pool_type: Optional[str] = None, connection: Optional[asyncpg.Connection] = None
) -> List[str]:
    """(Async) 获取所有系列（使用緩存優化）"""
    from gacha.services import series_cache

    return await series_cache.get_series_cache(pool_type=pool_type)


async def get_user_all_series_collection(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[SeriesCollection]:
    """(Async) 获取用户收集的所有系列信息"""
    series_list = await _get_all_series(connection=connection)
    collections_dict = await get_user_series_collections_batch(
        user_id, series_list, connection=connection
    )
    result = []
    for series in series_list:
        collection = collections_dict.get(series)
        if collection:
            result.append(collection)
    return result


async def get_user_series_collections_batch(
    user_id: int,
    series_list: List[str],
    pool_type: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, SeriesCollection]:
    """(Async) 批量获取用户对多个系列的收集信息"""
    if not series_list:
        return {}
    params_total: List[Any] = [series_list]
    params_collected: List[Any] = [user_id, series_list]
    pool_type_condition_total = ""
    pool_type_condition_collected = ""
    if pool_type:
        pool_type_condition_total = "AND pool_type = $2"
        params_total.append(pool_type)
        pool_type_condition_collected = "AND mc.pool_type = $3"
        params_collected.append(pool_type)
    total_counts_query = f"SELECT series, COUNT(DISTINCT card_id)::integer as count FROM gacha_master_cards WHERE series = ANY($1::text[]) {pool_type_condition_total} GROUP BY series"
    total_counts_result = await fetch_all(
        total_counts_query, params_total, connection=connection
    )
    total_counts = {row["series"]: row["count"] for row in total_counts_result}
    collected_counts_query = f"SELECT mc.series, COUNT(DISTINCT uc.card_id)::integer as count FROM {TABLE_NAME} uc JOIN gacha_master_cards mc ON uc.card_id = mc.card_id WHERE uc.user_id = $1 AND mc.series = ANY($2::text[]) {pool_type_condition_collected} GROUP BY mc.series"
    collected_counts_result = await fetch_all(
        collected_counts_query, params_collected, connection=connection
    )
    collected_counts = {row["series"]: row["count"] for row in collected_counts_result}
    result = {}
    for series in series_list:
        total_cards = total_counts.get(series, 0)
        if total_cards > 0:
            collected_cards = collected_counts.get(series, 0)
            result[series] = SeriesCollection(
                series=series, total_cards=total_cards, collected_cards=collected_cards
            )
    return result


async def get_overall_collection_stats(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """(Async) 獲取用戶所有系列的總體收集狀況"""
    query = f"""
        SELECT
            (SELECT COUNT(DISTINCT card_id)::integer FROM gacha_master_cards) as total_cards,
            COUNT(DISTINCT uc.card_id)::integer as total_collected
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.user_id = $1
    """
    result = await fetch_one(query, (user_id,), connection=connection)
    if not result:
        return {"total_cards": 0, "total_collected": 0}
    return {
        "total_collected": result.get("total_collected", 0),
        "total_cards": result.get("total_cards", 0),
    }


async def get_pool_specific_collection_stats(
    user_id: int, pool_type: str, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """(Async) 獲取用戶在特定卡池的收集統計"""
    query = f"""
        SELECT
            (SELECT COUNT(DISTINCT card_id)::integer FROM gacha_master_cards WHERE pool_type = $1) as total_cards,
            COUNT(DISTINCT uc.card_id)::integer as total_collected
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.user_id = $2 AND mc.pool_type = $1
    """
    result = await fetch_one(query, (pool_type, user_id), connection=connection)
    if not result:
        return {"total_cards": 0, "total_collected": 0}
    return {
        "total_cards": result.get("total_cards", 0),
        "total_collected": result.get("total_collected", 0),
    }


async def get_user_cards_summary(
    user_id: int,
    pool_type: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> List[Dict[str, Any]]:
    """(Async) 獲取用戶卡片摘要 (卡池, 稀有度, 唯一數量, 總數量)"""
    params: List[Any] = [user_id]
    pool_type_condition = ""
    if pool_type:
        pool_type_condition = "AND mc.pool_type = $2"
        params.append(pool_type)
    query = f"""
        SELECT
            mc.pool_type, mc.rarity,
            COUNT(DISTINCT uc.card_id)::integer as count,
            SUM(uc.quantity)::integer as total_quantity
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.user_id = $1 {pool_type_condition}
        GROUP BY mc.pool_type, mc.rarity
        ORDER BY mc.pool_type, mc.rarity
    """
    results = await fetch_all(query, params, connection=connection)
    return [dict(row) for row in results]


async def get_rarities_total_count(
    pool_type_filter: Optional[str] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Dict[int, int]]:
    """(Async) 獲取各卡池各稀有度卡片的總數"""
    params: List[Any] = []
    pool_filter_condition_sql = ""
    if pool_type_filter:
        params.append(pool_type_filter)
        pool_filter_condition_sql = "WHERE pool_type = $1"
    query = f"""
        SELECT pool_type, rarity, COUNT(card_id)::integer as count
        FROM gacha_master_cards
        {pool_filter_condition_sql}
        GROUP BY pool_type, rarity
        ORDER BY pool_type, rarity
    """
    results = await fetch_all(query, params, connection=connection)
    output: Dict[str, Dict[int, int]] = {}
    if results:
        for row in results:
            pool = row["pool_type"]
            rarity_value = row["rarity"]
            count = row["count"]
            pool_data = output.setdefault(pool, {})
            pool_data[rarity_value] = count
    return output


# --- 功能 (Features) Mixin 函式 ---


async def _execute_set_is_favorite_db(
    user_id: int,
    card_id: int,
    is_favorite: bool,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """(Async) 實際執行資料庫更新 is_favorite 的操作"""
    query = (
        f"UPDATE {TABLE_NAME} SET is_favorite = $1 WHERE user_id = $2 AND card_id = $3"
    )
    status_msg = await execute_query(
        query, (is_favorite, user_id, card_id), connection=connection
    )
    if status_msg != 1:
        raise CardNotFoundError(f"您沒有這張卡片 (ID: {card_id})", card_id=card_id)
    return True


async def _update_favorite_status(
    user_id: int,
    card_id: int,
    status: bool,
    return_is_favorite: bool = True,
    connection: Optional[asyncpg.Connection] = None,
) -> Any:
    """(Async) 更新卡片的 is_favorite 欄位 (內部輔助方法)"""
    await _execute_set_is_favorite_db(user_id, card_id, status, connection=connection)
    return status if return_is_favorite else True


async def toggle_favorite_card(
    user_id: int, card_id: int, connection: Optional[asyncpg.Connection] = None
) -> bool:
    """(Async) 切換卡片的最愛狀態. 成功返回新的狀態 (True/False)."""
    check_query = (
        f"SELECT is_favorite FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2"
    )
    result = await fetch_one(check_query, (user_id, card_id), connection=connection)
    if not result:
        logger.warning(
            "[GACHA_FEATURE_MIXIN] 嘗試切換不存在的收藏卡片: user_id=%s, card_id=%s",
            user_id,
            card_id,
        )
        raise ValueError(f"您沒有這張卡片 (ID: {card_id})")
    current_state = result.get("is_favorite", False)
    new_state = not current_state
    updated_status_val = await _update_favorite_status(
        user_id, card_id, new_state, return_is_favorite=True, connection=connection
    )
    if updated_status_val == new_state:
        return new_state
    else:
        logger.error(
            "[GACHA_FEATURE_MIXIN] toggle_favorite_card: _update_favorite_status 返回意外狀態: user=%s, card=%s, expected=%s, got=%s",
            user_id,
            card_id,
            new_state,
            updated_status_val,
        )
        raise RepositoryError("切換最愛狀態後狀態不一致")


async def set_favorite_status(
    user_id: int,
    card_id: int,
    status: bool,
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """(Async) 直接設置卡片的最愛狀態. 成功返回True."""
    check_query = (
        f"SELECT is_favorite FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = $2"
    )
    result = await fetch_one(check_query, (user_id, card_id), connection=connection)
    if not result:
        logger.warning(
            "[GACHA_FEATURE_MIXIN] 嘗試設置不存在的收藏卡片最愛狀態: user_id=%s, card_id=%s",
            user_id,
            card_id,
        )
        raise ValueError(f"您沒有這張卡片 (ID: {card_id})")
    current_state = result.get("is_favorite", False)
    if current_state == status:
        return True
    return await _update_favorite_status(
        user_id, card_id, status, return_is_favorite=False, connection=connection
    )


async def get_owned_card_ids_in_batch(
    user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> List[int]:
    """批量獲取用戶在指定列表中擁有的卡片ID"""
    if not card_ids:
        return []
    query = f"SELECT card_id FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = ANY($2::int[])"
    results = await fetch_all(query, [user_id, card_ids], connection=connection)
    return [row["card_id"] for row in results]


async def get_favorite_card_ids_in_batch(
    user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> List[int]:
    """批量獲取在指定列表中已被設為最愛的卡片ID"""
    if not card_ids:
        return []
    query = f"SELECT card_id FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = ANY($2::int[]) AND is_favorite = TRUE"
    results = await fetch_all(query, [user_id, card_ids], connection=connection)
    return [row["card_id"] for row in results]


async def get_cards_for_batch_favorite_sorted(
    user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """
    獲取用於批次加入最愛的卡片詳細訊息，並直接在資料庫中排序。
    排序規則：稀有度(rarity)降序，然後卡片ID(card_id)升序。
    """
    if not card_ids:
        return []
    query = f"""
        SELECT
            uc.card_id,
            mc.rarity
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.user_id = $1 AND uc.card_id = ANY($2::integer[])
        ORDER BY mc.rarity DESC, mc.card_id ASC;
    """
    results = await fetch_all(query, [user_id, card_ids], connection=connection)
    return [dict(row) for row in results]


# --- 排序 (Sort) Mixin 函式 ---


async def get_card_sort_indexes(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[int, Optional[int]]:
    """(Async) 獲取用戶所有卡片的自定義排序索引."""
    query = f"SELECT card_id, custom_sort_index FROM {TABLE_NAME} WHERE user_id = $1 ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC"
    results = await fetch_all(query, (user_id,), connection=connection)
    return (
        {result["card_id"]: result["custom_sort_index"] for result in results}
        if results
        else {}
    )


async def get_min_custom_sort_index(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[int]:
    """(Async) 獲取用戶已排序卡片中最小的排序索引值."""
    query = f"SELECT MIN(custom_sort_index) as min_index FROM {TABLE_NAME} WHERE user_id = $1 AND custom_sort_index IS NOT NULL"
    return await fetch_value(query, (user_id,), connection=connection)


async def get_max_custom_sort_index(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> Optional[int]:
    """(Async) 獲取用戶已排序卡片中最大的排序索引值."""
    query = f"SELECT MAX(custom_sort_index) as max_index FROM {TABLE_NAME} WHERE user_id = $1 AND custom_sort_index IS NOT NULL"
    return await fetch_value(query, (user_id,), connection=connection)


async def count_sorted_cards(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """(Async) 計算用戶已排序卡片的數量."""
    query = f"SELECT COUNT(*)::integer as count FROM {TABLE_NAME} WHERE user_id = $1 AND custom_sort_index IS NOT NULL"
    count = await fetch_value(query, (user_id,), connection=connection)
    return count or 0


async def count_sorted_cards_with_filters(
    user_id: int,
    filters: CollectionFilters,
    connection: Optional[asyncpg.Connection] = None,
) -> int:
    """(Async) 計算用戶在指定篩選條件下已排序卡片的數量."""
    if filters is None:
        filters = CollectionFilters()
    where_clause, params = _build_filter_conditions(user_id, filters)
    where_clause += " AND uc.custom_sort_index IS NOT NULL"
    query = f"SELECT COUNT(*)::integer as count FROM {TABLE_NAME} uc JOIN gacha_master_cards mc ON uc.card_id = mc.card_id WHERE {where_clause}"
    count = await fetch_value(query, params, connection=connection)
    return count or 0


async def get_card_position(
    user_id: int, card_id: int, connection: Optional[asyncpg.Connection] = None
) -> int:
    """(Async) 獲取卡片的當前位置 (基於自定義排序). 未找到則拋出 CardNotFoundError."""
    query = f"""
        WITH ranked_cards AS (
            SELECT card_id, custom_sort_index,
                   ROW_NUMBER() OVER (ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC) as rank
            FROM {TABLE_NAME}
            WHERE user_id = $1
        )
        SELECT rank, custom_sort_index
        FROM ranked_cards
        WHERE card_id = $2
    """
    result = await fetch_one(query, (user_id, card_id), connection=connection)
    if result and result["custom_sort_index"] is not None and ("rank" in result):
        return result["rank"]
    raise CardNotFoundError(f"找不到卡片 {card_id} 的排序位置 for user {user_id}")


async def get_card_sort_info(
    user_id: int, card_id: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """(Async) 獲取卡片的排序信息 (索引值和排名). 未找到則拋出 CardNotFoundError."""
    query = f"""
        WITH ranked_cards AS (
            SELECT card_id, custom_sort_index,
                   ROW_NUMBER() OVER (ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC) as rank
            FROM {TABLE_NAME}
            WHERE user_id = $1
        )
        SELECT rc.custom_sort_index as sort_index, rc.rank
        FROM ranked_cards rc
        WHERE rc.card_id = $2
    """
    result = await fetch_one(query, (user_id, card_id), connection=connection)
    if result:
        return {"sort_index": result.get("sort_index"), "rank": result.get("rank")}
    raise CardNotFoundError(f"找不到卡片 {card_id} 的排序信息 for user {user_id}")


async def get_card_at_position(
    user_id: int, position: int, connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """(Async) 根據位置獲取已排序卡片信息 (card_id, sort_index). 未找到則拋出 CardNotFoundError."""
    if position < 1:
        raise ValueError("位置參數必須大於等於1")
    query = f"""
        WITH ranked_cards AS (
            SELECT card_id, custom_sort_index,
                   ROW_NUMBER() OVER (ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC) as rank
            FROM {TABLE_NAME}
            WHERE user_id = $1 AND custom_sort_index IS NOT NULL
        )
        SELECT card_id, custom_sort_index as sort_index
        FROM ranked_cards
        WHERE rank = $2
    """
    result = await fetch_one(query, (user_id, position), connection=connection)
    if not result:
        raise CardNotFoundError(
            f"在位置 {position} 找不到已排序的卡片 for user {user_id}"
        )
    return dict(result)


async def get_cards_by_rank_range(
    user_id: int,
    start_position: int,
    window_size: int,
    connection: Optional[asyncpg.Connection] = None,
) -> List[Dict[str, Any]]:
    """(Async) 根據排名範圍獲取已排序卡片信息列表 (card_id, sort_index)."""
    if start_position < 1:
        raise ValueError("起始位置參數必須大於等於1")
    if window_size < 1:
        raise ValueError("窗口大小參數必須大於等於1")

    query = f"""
        WITH ranked_cards AS (
            SELECT card_id, custom_sort_index,
                   ROW_NUMBER() OVER (ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC) as rank
            FROM {TABLE_NAME}
            WHERE user_id = $1 AND custom_sort_index IS NOT NULL
        )
        SELECT card_id, custom_sort_index as sort_index
        FROM ranked_cards
        WHERE rank >= $2 AND rank < $3
        ORDER BY rank
    """
    end_position = start_position + window_size
    results = await fetch_all(
        query, (user_id, start_position, end_position), connection=connection
    )
    return [dict(row) for row in results] if results else []


async def get_prev_sorted_card(
    user_id: int,
    current_sort_index: int,
    connection: Optional[asyncpg.Connection] = None,
) -> Optional[Dict[str, Any]]:
    """(Async) 獲取排序索引小於指定值的前一張已排序卡片."""
    query = f"SELECT card_id, custom_sort_index as sort_index FROM {TABLE_NAME} WHERE user_id = $1 AND custom_sort_index IS NOT NULL AND custom_sort_index < $2 ORDER BY custom_sort_index DESC LIMIT 1"
    result = await fetch_one(
        query, (user_id, current_sort_index), connection=connection
    )
    return dict(result) if result else None


async def get_next_sorted_card(
    user_id: int,
    current_sort_index: int,
    connection: Optional[asyncpg.Connection] = None,
) -> Optional[Dict[str, Any]]:
    """(Async) 獲取排序索引大於指定值的下一張已排序卡片."""
    query = f"SELECT card_id, custom_sort_index as sort_index FROM {TABLE_NAME} WHERE user_id = $1 AND custom_sort_index IS NOT NULL AND custom_sort_index > $2 ORDER BY custom_sort_index ASC LIMIT 1"
    result = await fetch_one(
        query, (user_id, current_sort_index), connection=connection
    )
    return dict(result) if result else None


async def get_all_sorted_cards(
    user_id: int, connection: Optional[asyncpg.Connection] = None
) -> List[Dict[str, Any]]:
    """(Async) 獲取用戶所有已排序卡片信息 (card_id, sort_index, name, rarity)."""
    query = f"SELECT uc.card_id, uc.custom_sort_index, c.name, c.rarity FROM {TABLE_NAME} uc JOIN gacha_master_cards c ON uc.card_id = c.card_id WHERE uc.user_id = $1 AND uc.custom_sort_index IS NOT NULL ORDER BY uc.custom_sort_index ASC, uc.card_id ASC"
    results = await fetch_all(query, (user_id,), connection=connection)
    return [dict(row) for row in results] if results else []


async def get_card_with_sort_index(
    user_id: int,
    sort_index: int,
    exclude_card_id: Optional[int] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Optional[Dict[str, Any]]:
    """(Async) 查找具有特定排序索引值的卡片."""
    params: List[Any] = [user_id, sort_index]
    exclude_clause = ""
    if exclude_card_id is not None:
        exclude_clause = f" AND card_id != ${len(params) + 1}"
        params.append(exclude_card_id)
    query = f"SELECT card_id, custom_sort_index as sort_index FROM {TABLE_NAME} WHERE user_id = $1 AND custom_sort_index = $2{exclude_clause} LIMIT 1"
    result = await fetch_one(query, params, connection=connection)
    return dict(result) if result else None


async def get_user_card_position(
    user_id: int,
    card_id: int,
    sort_by: str = "rarity",
    sort_order: str = "desc",
    filters: Optional[CollectionFilters] = None,
    connection: Optional[asyncpg.Connection] = None,
    favorite_priority: bool = True,
) -> Dict[str, Any]:
    """(Async) 獲取用戶卡片在篩選和排序後的列表中的位置、總數以及卡片的 custom_sort_index."""
    if filters is None:
        filters = CollectionFilters()
    where_clause, filter_params = _build_filter_conditions(user_id, filters)
    order_clause = _build_sorting_clause(sort_by, sort_order, favorite_priority)
    card_id_placeholder_index = len(filter_params) + 1
    final_params = filter_params + [card_id]
    if not GET_USER_CARD_POSITION_SQL or not isinstance(
        GET_USER_CARD_POSITION_SQL, str
    ):
        logger.error(
            "[GACHA_SORT_MIXIN] GET_USER_CARD_POSITION_SQL is not a valid query string template."
        )
        raise RepositoryError(
            "Query template for card position is not configured correctly."
        )
    query = GET_USER_CARD_POSITION_SQL.format(
        order_clause=order_clause,
        table_name=TABLE_NAME,
        where_clause=where_clause,
        card_id_placeholder_index=f"${card_id_placeholder_index}",
    )
    result = await fetch_one(query, final_params, connection=connection)
    if result and result.get("position") is not None:
        return {
            "position": result["position"],
            "total": result.get("total", 0),
            "custom_sort_index": result.get("custom_sort_index"),
        }
    logger.info(
        "[GACHA_SORT_MIXIN] Card ID %s for user %s not found or position is null with current filters/sort.",
        card_id,
        user_id,
    )
    raise CardNotFoundError(
        f"Card {card_id} not found in the filtered and sorted list for user {user_id}."
    )


# --- 批量操作 (Bulk) Mixin 函式 ---


async def delete_cards_by_ids_raw(
    user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection] = None
) -> Dict[str, Any]:
    """根據 ID 列表批量刪除用戶的卡片。"""
    if not card_ids:
        return {"deleted_rows": 0, "deleted_cards_info": []}
    details_query = f"""
        SELECT uc.card_id, uc.quantity, mc.rarity, mc.pool_type, mc.name, uc.is_favorite
        FROM {TABLE_NAME} uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.user_id = $1 AND uc.card_id = ANY($2::integer[])
    """
    try:
        results = await fetch_all(
            details_query, [user_id, card_ids], connection=connection
        )
        if not results:
            return {"deleted_rows": 0, "deleted_cards_info": []}
        deleted_cards_details = [
            {
                "id": r["card_id"],
                "quantity": r["quantity"],
                "rarity": r["rarity"],
                "pool_type": r["pool_type"],
                "name": r["name"],
                "is_favorite": r.get("is_favorite", False),
            }
            for r in results
        ]
        actual_card_ids_to_delete = [d["id"] for d in deleted_cards_details]
        delete_query = f"DELETE FROM {TABLE_NAME} WHERE user_id = $1 AND card_id = ANY($2::integer[])"
        status = await execute_query(
            delete_query, [user_id, actual_card_ids_to_delete], connection=connection
        )
        deleted_rows = 0
        if isinstance(status, str) and "DELETE" in status:
            try:
                deleted_rows = int(status.split(" ")[1])
            except (IndexError, ValueError):
                deleted_rows = len(deleted_cards_details)
        return {
            "deleted_rows": deleted_rows,
            "deleted_cards_info": deleted_cards_details,
        }
    except Exception as e:
        logger.error(
            f"批量刪除卡片失敗，用戶ID: {user_id}，卡片IDs: {card_ids}, 錯誤: {str(e)}"
        )
        raise DatabaseOperationError(
            f"批量刪除卡片時資料庫操作失敗: {str(e)}", original_exception=e
        ) from e


async def set_custom_sort_index(
    user_id: int,
    card_id: int,
    sort_index: Optional[int],
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """(Async) 設置單張卡片的自定義排序索引"""
    query = f"UPDATE {TABLE_NAME} SET custom_sort_index = $1 WHERE user_id = $2 AND card_id = $3"
    result = await execute_query(
        query, (sort_index, user_id, card_id), connection=connection
    )
    return result is not None


async def bulk_update_sort_indexes(
    user_id: int,
    updates: Union[List[Dict[str, Any]], List[Tuple[int, Optional[int]]]],
    connection: Optional[asyncpg.Connection] = None,
) -> bool:
    """(Async) 批量更新多張卡片的排序索引

    參數:
        user_id: 用戶ID
        updates: 更新列表，可以是字典列表或元組列表
                字典格式: [{'card_id': int, 'sort_index': Optional[int]}, ...]
                元組格式: [(card_id: int, sort_index: Optional[int]), ...]
        connection: 資料庫連接
    """
    if not updates:
        return True

    update_params = []
    for update in updates:
        if isinstance(update, dict):
            # 字典格式
            card_id = update.get("card_id")
            sort_index = update.get("sort_index")
        elif isinstance(update, (tuple, list)) and len(update) >= 2:
            # 元組格式
            card_id = update[0]
            sort_index = update[1]
        else:
            logger.warning("[GACHA] bulk_update_sort: 無效的更新格式: %s", update)
            continue

        if card_id is None:
            logger.warning("[GACHA] bulk_update_sort: 缺少 card_id: %s", update)
            continue
        update_params.append((sort_index, user_id, card_id))

    if not update_params:
        return True
    query = f"UPDATE {TABLE_NAME} SET custom_sort_index = $1 WHERE user_id = $2 AND card_id = $3"
    await execute_many(query, update_params, connection=connection)
    return True


async def batch_set_favorite_status_raw(
    user_id: int,
    card_ids: List[int],
    status: bool,
    sort_indexes_map: Optional[Dict[int, int]] = None,
    connection: Optional[asyncpg.Connection] = None,
) -> Dict[str, Any]:
    """(Async) 批量設置卡片的最愛狀態 (原子操作)。"""
    if not card_ids:
        return {"updated_count": 0, "changed_favorites": []}
    try:
        total_updated_count = 0
        batch_size = 1000
        changed_favorites_details: List[Tuple[int, int]] = []
        delta = 1 if status else -1
        for i in range(0, len(card_ids), batch_size):
            card_ids_batch = card_ids[i : i + batch_size]
            batch_update_query = ""
            params: List[Any] = []
            if status:
                if sort_indexes_map:
                    update_values = [
                        (cid, sort_indexes_map[cid])
                        for cid in card_ids_batch
                        if cid in sort_indexes_map
                    ]
                    if not update_values:
                        batch_update_query = f"UPDATE {TABLE_NAME} SET is_favorite = TRUE WHERE user_id = $1 AND card_id = ANY($2::integer[]) AND (is_favorite = FALSE OR is_favorite IS NULL) RETURNING card_id;"
                        params = [user_id, card_ids_batch]
                    else:
                        value_placeholders = [
                            f"(${j * 2 + 1}::integer, ${j * 2 + 2}::integer)"
                            for j in range(len(update_values))
                        ]
                        flat_params = [
                            item for sublist in update_values for item in sublist
                        ]
                        user_id_idx = len(flat_params) + 1
                        card_ids_idx = user_id_idx + 1
                        params = flat_params + [user_id, [v[0] for v in update_values]]
                        batch_update_query = f"""
                            WITH updates (card_id, new_sort_index) AS (VALUES {", ".join(value_placeholders)})
                            UPDATE {TABLE_NAME} uc SET is_favorite = TRUE, custom_sort_index = u.new_sort_index
                            FROM updates u WHERE uc.user_id = ${user_id_idx} AND uc.card_id = u.card_id
                            AND uc.card_id = ANY(${card_ids_idx}::integer[]) AND (uc.is_favorite = FALSE OR uc.is_favorite IS NULL)
                            RETURNING uc.card_id;
                        """
                else:
                    batch_update_query = f"UPDATE {TABLE_NAME} SET is_favorite = TRUE WHERE user_id = $1 AND card_id = ANY($2::integer[]) AND (is_favorite = FALSE OR is_favorite IS NULL) RETURNING card_id;"
                    params = [user_id, card_ids_batch]
            else:
                batch_update_query = f"UPDATE {TABLE_NAME} SET is_favorite = FALSE, custom_sort_index = NULL WHERE user_id = $1 AND card_id = ANY($2::integer[]) AND is_favorite = TRUE RETURNING card_id;"
                params = [user_id, card_ids_batch]
            if batch_update_query and params and card_ids_batch:
                returned_rows = await fetch_all(
                    batch_update_query, params, connection=connection
                )
                if returned_rows:
                    total_updated_count += len(returned_rows)
                    for row in returned_rows:
                        changed_favorites_details.append((row["card_id"], delta))
        return {
            "updated_count": total_updated_count,
            "changed_favorites": changed_favorites_details,
        }
    except Exception as e:
        logger.error(
            f"批量設置最愛狀態失敗，用戶ID: {user_id}，卡片IDs: {len(card_ids)}, 錯誤: {str(e)}"
        )
        raise DatabaseOperationError(
            f"批量設置最愛狀態時資料庫操作失敗: {str(e)}", original_exception=e
        ) from e


async def get_cards_info_for_selling_raw(
    user_id: int,
    favorite_condition_sql: str,
    quantity_condition_sql: str,
    sell_quantity_expr_sql: str,
    connection: asyncpg.Connection,
) -> List[Dict[str, Any]]:
    """(Async) 根據提供的 SQL 片段獲取準備賣出的卡片信息列表 (原子操作)。"""
    if connection is None:
        raise ValueError(
            "A valid database connection must be provided for get_cards_info_for_selling_raw."
        )
    try:
        stats_query = f"""
            WITH card_stats AS (
                SELECT uc.card_id, uc.quantity, mc.name, mc.rarity, mc.pool_type, uc.is_favorite, {sell_quantity_expr_sql} as sell_quantity
                FROM {TABLE_NAME} uc JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
                WHERE uc.user_id = $1 AND {quantity_condition_sql} {favorite_condition_sql}
            )
            SELECT jsonb_agg(jsonb_build_object('card_id', card_id, 'quantity', quantity, 'name', name, 'rarity', rarity, 'pool_type', pool_type, 'is_favorite', is_favorite, 'sell_quantity', sell_quantity)) FILTER (WHERE sell_quantity > 0) as cards_info
            FROM card_stats
        """
        result = await connection.fetchrow(stats_query, user_id)
        if not result or not result["cards_info"]:
            return []
        return (
            json.loads(result["cards_info"])
            if isinstance(result["cards_info"], str)
            else []
        )
    except json.JSONDecodeError as e:
        logger.error(
            "[GACHA] get_cards_info_for_selling_raw: JSON解碼失敗: %s", e, exc_info=True
        )
        raise RuntimeError(f"解析售賣卡片信息時JSON解碼失敗: {e}") from e
    except asyncpg.PostgresError as e:
        logger.error(
            "[GACHA] get_cards_info_for_selling_raw: 資料庫操作出錯: %s",
            e,
            exc_info=True,
        )
        raise RuntimeError(f"獲取售賣卡片信息時資料庫操作出錯: {e}") from e
    except Exception as e:
        logger.error(
            "[GACHA] get_cards_info_for_selling_raw: 發生未知錯誤: %s", e, exc_info=True
        )
        raise RuntimeError(f"獲取售賣卡片信息時發生未知錯誤: {e}") from e
